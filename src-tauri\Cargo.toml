[package]
name = "privacy-ai-assistant"
version = "0.1.0"
authors = ["Narisetti Chaitanya Naidu <chait<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com.com>"]
edition = "2021"
license = "MIT"

[lib]
name = "privacy_ai_assistant_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

# Use supported versions
[build-dependencies]
tauri-build = { version = "2.0", features = [], default-features = false }

[dependencies]
tauri = { version = "2.0", features = [] }
tauri-plugin-shell = "2.0"
tauri-plugin-http = "2.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
log = "0.4"
env_logger = "0.10"
base64 = "0.22"
futures-util = "0.3"
chrono = { version = "0.4", features = ["serde"] }
reqwest = { version = "0.11", features = ["json"] }
thiserror = "1.0"
urlencoding = "2.1"
# STT/TTS dependencies
cpal = "0.15"
hound = "3.5"
rodio = "0.17"
byteorder = "1.5"

[features]
default = ["custom-protocol"]
custom-protocol = ["tauri/custom-protocol"]

[package.metadata.tauri]
bundle.identifier = "com.privacy.ai.assistant"
windows.webviewInstallMode = "downloadBootstrapper"
