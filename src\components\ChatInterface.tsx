import React, { useEffect, useState, useRef } from 'react';
import MessageBubble from './MessageBubble';
import { VoiceRecordingModal } from './VoiceRecordingModal';
import RealtimeVoiceModal from './RealtimeVoiceModal';
import RealtimeConversationModal from './RealtimeConversationModal';
import AudioDiagnosticPanel from './AudioDiagnosticPanel';
import InputArea from './InputArea';
import ModelStatusBadge from './ModelStatusBadge';
import HardwareStatusBadge from './HardwareStatusBadge';
import ThinkingIndicator from './ThinkingIndicator';
import StartupDiagnostic from './StartupDiagnostic';
import { useMultiChatStore } from '../stores/chatStore';
import { modelHealthChecker, ModelHealthStatus } from '../utils/modelHealth';
import { Settings } from 'lucide-react';

const ChatInterface: React.FC = () => {
  const [showVoiceModal, setShowVoiceModal] = useState(false);
  const [showRealtimeVoiceModal, setShowRealtimeVoiceModal] = useState(false);
  const [showRealtimeConversationModal, setShowRealtimeConversationModal] = useState(false);
  const [showAudioDiagnostic, setShowAudioDiagnostic] = useState(false);
  const [systemReady, setSystemReady] = useState(false);
  const [showDiagnostic, setShowDiagnostic] = useState(true);
  const [modelHealth, setModelHealth] = useState<ModelHealthStatus | null>(null);

  const { messages, addMessage, setLoading, isLoading } = useMultiChatStore();
  const chatWindowRef = useRef<HTMLDivElement>(null);

  // Subscribe to model health status
  useEffect(() => {
    const unsubscribe = modelHealthChecker.subscribe((status) => {
      setModelHealth(status);
    });

    // Start periodic health checks
    modelHealthChecker.startPeriodicCheck(15000);

    return unsubscribe;
  }, []);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (chatWindowRef.current) {
      chatWindowRef.current.scrollTop = chatWindowRef.current.scrollHeight;
    }
  }, [messages]);

  const handleSendMessage = async (message: string) => {
    if (!message.trim() || isLoading) return;

    try {
      setLoading(true);

      // Add user message
      addMessage(message, 'user');

      // Generate response using Python backend
      const response = await fetch('http://127.0.0.1:8000/llm/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: message,
          model: 'gemma3n:latest',
          stream: false
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      // Add assistant response
      addMessage(result.response || 'No response received', 'assistant');

    } catch (error) {
      console.error('Failed to send message:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

      // Add error message
      addMessage(`Error: ${errorMessage}`, 'assistant');
    } finally {
      setLoading(false);
    }
  };

  const handleVoiceRecord = () => {
    setShowVoiceModal(true);
  };

  const handleVoiceTranscription = (text: string) => {
    if (text.trim()) {
      handleSendMessage(text);
    }
  };

  const handleVoiceRecordingStateChange = (isRecording: boolean) => {
    // Handle recording state changes if needed
    console.log('Voice recording state:', isRecording);
  };

  const handleDiagnosticComplete = (success: boolean) => {
    console.log('Diagnostic complete:', { success });
    setSystemReady(success);

    // Auto-hide diagnostic after 3 seconds if successful
    if (success) {
      setTimeout(() => {
        setShowDiagnostic(false);
      }, 3000);
    }
  };

  return (
    <div className="flex flex-col h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            Privacy AI Assistant
          </h1>
          
          <div className="flex items-center space-x-2">
            <ModelStatusBadge
              status={modelHealth || {
                isAvailable: false,
                isChecking: false,
                lastChecked: null,
                error: 'Not initialized',
                connectionState: 'disconnected' as const,
                modelName: 'gemma3n:latest',
                serviceUrl: 'http://localhost:11434',
                lastSuccessfulCheck: null
              }}
              onRefresh={() => modelHealthChecker.forceCheck()}
            />
            <HardwareStatusBadge />
            
            {/* Diagnostic Toggle */}
            <button
              type="button"
              onClick={() => setShowDiagnostic(!showDiagnostic)}
              className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              title="Toggle diagnostic"
            >
              <Settings className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Startup Diagnostic */}
      {showDiagnostic && (
        <StartupDiagnostic onDiagnosticComplete={handleDiagnosticComplete} />
      )}

      {/* Chat Messages */}
      <div 
        ref={chatWindowRef}
        className="flex-1 overflow-y-auto p-4 space-y-4"
        style={{ scrollBehavior: 'smooth' }}
      >
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 dark:text-gray-400 mt-8">
            <p className="text-lg mb-2">Welcome to Privacy AI Assistant</p>
            <p className="text-sm">Start a conversation by typing a message or using voice input</p>
          </div>
        ) : (
          messages.map((message) => (
            <MessageBubble
              key={message.id}
              message={message}
            />
          ))
        )}
        
        {/* Thinking Indicator */}
        {isLoading && <ThinkingIndicator isVisible={isLoading} />}
      </div>

      {/* Input Area */}
      <InputArea
        onSendMessage={handleSendMessage}
        onVoiceRecord={handleVoiceRecord}
        disabled={!systemReady}
        isLoading={isLoading}
      />

      {/* Voice Recording Modal */}
      {showVoiceModal && (
        <VoiceRecordingModal
          isOpen={showVoiceModal}
          onClose={() => setShowVoiceModal(false)}
          onTranscriptionComplete={handleVoiceTranscription}
          onRecordingStateChange={handleVoiceRecordingStateChange}
        />
      )}

      {/* Real-time Voice Recording Modal */}
      {showRealtimeVoiceModal && (
        <RealtimeVoiceModal
          isOpen={showRealtimeVoiceModal}
          onClose={() => setShowRealtimeVoiceModal(false)}
          onTranscriptionComplete={handleVoiceTranscription}
          onRecordingStateChange={handleVoiceRecordingStateChange}
        />
      )}

      {/* Real-time Conversation Modal */}
      {showRealtimeConversationModal && (
        <RealtimeConversationModal
          isOpen={showRealtimeConversationModal}
          onClose={() => setShowRealtimeConversationModal(false)}
        />
      )}

      {/* Audio Diagnostic Panel */}
      {showAudioDiagnostic && (
        <AudioDiagnosticPanel
          isOpen={showAudioDiagnostic}
          onClose={() => setShowAudioDiagnostic(false)}
        />
      )}
    </div>
  );
};

export default ChatInterface;
